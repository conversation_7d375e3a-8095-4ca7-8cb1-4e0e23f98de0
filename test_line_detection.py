#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试线条检测效果
"""

import cv2
import numpy as np
from parallel_line_detector import ParallelLineDetector

def test_line_detection(image_path):
    """
    测试线条检测效果
    """
    print("=" * 80)
    print("🔍 测试线条检测效果")
    print("=" * 80)

    # 创建检测器实例，使用宽松参数
    detector = ParallelLineDetector(
        angle_threshold=8.0,    # 宽松的角度容忍度
        distance_threshold=80,  # 宽松的距离阈值
        min_line_length=20,     # 较低的最小线段长度
        max_line_gap=30         # 较大的线段间隙容忍度
    )

    try:
        print(f"开始检测图像: {image_path}")
        parallel_groups, original_image, edges, gray, all_lines = detector.detect_parallel_lines(
            image_path, debug=True
        )

        # 统计结果
        total_groups = len(parallel_groups)
        total_lines = sum(len(group) for group in parallel_groups)
        total_pairs, group_pairs = detector.count_parallel_pairs(parallel_groups)

        print(f"\n📊 检测结果:")
        print(f"  - 检测到的所有线条: {len(all_lines) if all_lines else 0}")
        print(f"  - 平行线组数: {total_groups}")
        print(f"  - 平行线条数: {total_lines}")
        print(f"  - 平行线对数: {total_pairs}")

        # 输出每组的详细信息
        for i, group in enumerate(parallel_groups):
            angle = group[0][1]['angle']
            print(f"    第 {i+1} 组: {len(group)} 条线, 角度: {angle:.1f}°")

        # 可视化结果
        result_image = detector.visualize_results(
            original_image,
            parallel_groups,
            all_lines,
            save_path="line_detection_result.jpg",
            show_all_lines=True
        )

        # 保存边缘检测结果
        cv2.imwrite("edges_result.jpg", edges)

        print(f"\n✅ 检测完成!")
        print(f"📁 结果图像: line_detection_result.jpg")
        print(f"📁 边缘图像: edges_result.jpg")

        return {
            "total_lines": len(all_lines) if all_lines else 0,
            "parallel_groups": total_groups,
            "parallel_lines": total_lines,
            "parallel_pairs": total_pairs
        }

    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 测试指定图像
    image_path = "D:/1/7.jpg"  # 根据您的图像路径调整
    result = test_line_detection(image_path)
    
    if result:
        print(f"\n🎯 总结:")
        print(f"检测到 {result['total_lines']} 条线条")
        print(f"形成 {result['parallel_groups']} 组平行线")
        print(f"包含 {result['parallel_lines']} 条平行线")
        print(f"共 {result['parallel_pairs']} 对平行线")
