import cv2
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
import math
from typing import List, Tu<PERSON>, Dict

class ParallelLineDetector:
    def __init__(self, angle_threshold=5.0, distance_threshold=50, min_line_length=30, max_line_gap=15):
        """
        初始化平行线检测器

        Args:
            angle_threshold: 角度阈值（度），用于判断两条线是否平行
            distance_threshold: 距离阈值，用于判断平行线是否属于同一组
            min_line_length: 最小线段长度
            max_line_gap: 线段间最大间隙
        """
        self.angle_threshold = angle_threshold
        self.distance_threshold = distance_threshold
        self.min_line_length = min_line_length
        self.max_line_gap = max_line_gap
    
    def preprocess_image(self, image):
        """
        图像预处理：转换为灰度图并进行边缘检测，专门优化粗线条检测
        """
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # 高斯模糊减少噪声
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)

        # 检测粗线条并进行骨架化处理
        edges = self.detect_thick_lines_with_skeletonization(blurred)

        # 计算边缘像素比例
        edge_pixels = np.sum(edges > 0)
        total_pixels = edges.shape[0] * edges.shape[1]
        edge_ratio = edge_pixels / total_pixels

        print(f"边缘像素比例: {edge_ratio:.4f}")

        return edges, gray

    def detect_thick_lines_with_skeletonization(self, gray_image):
        """
        专门处理粗线条的检测方法，减少噪声和过多边缘
        """
        # 1. 使用更保守的二值化，减少噪声
        # 使用Otsu阈值法进行全局二值化
        _, binary = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 2. 更强的形态学操作去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

        # 3. 进一步去除小的连通区域
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(binary, connectivity=8)
        min_area = 200  # 最小连通区域面积
        filtered_binary = np.zeros_like(binary)

        for i in range(1, num_labels):  # 跳过背景（标签0）
            if stats[i, cv2.CC_STAT_AREA] >= min_area:
                filtered_binary[labels == i] = 255

        # 4. 骨架化处理 - 将粗线条转换为单像素宽度的中心线
        skeleton = self.skeletonize(filtered_binary)

        # 5. 如果骨架化结果太少，使用边缘检测
        skeleton_pixels = np.sum(skeleton > 0)
        if skeleton_pixels < 15:  # 进一步降低阈值
            print("骨架化结果较少，使用边缘检测...")
            # 使用较低阈值的Canny边缘检测，检测更多边缘
            median_val = np.median(gray_image)
            lower_threshold = int(max(20, 0.4 * median_val))  # 进一步降低下阈值
            upper_threshold = int(min(220, 1.4 * median_val))  # 提高上阈值
            edges = cv2.Canny(gray_image, lower_threshold, upper_threshold, apertureSize=3)

            # 轻微的形态学操作连接断裂的边缘
            kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 1))
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_close)
            return edges
        else:
            print(f"骨架化成功，检测到 {skeleton_pixels} 个骨架像素")
            return skeleton

    def skeletonize(self, binary_image):
        """
        骨架化算法，将粗线条转换为单像素宽度的中心线
        """
        # 使用OpenCV的形态学骨架化
        skeleton = np.zeros(binary_image.shape, np.uint8)
        element = cv2.getStructuringElement(cv2.MORPH_CROSS, (3, 3))

        while True:
            # 开运算
            opened = cv2.morphologyEx(binary_image, cv2.MORPH_OPEN, element)
            # 从原图像中减去开运算结果
            temp = cv2.subtract(binary_image, opened)
            # 腐蚀
            eroded = cv2.erode(binary_image, element)
            # 将结果添加到骨架
            skeleton = cv2.bitwise_or(skeleton, temp)
            binary_image = eroded.copy()

            # 如果没有更多像素可以腐蚀，停止
            if cv2.countNonZero(binary_image) == 0:
                break

        return skeleton

    def detect_lines(self, edges):
        """
        使用霍夫变换检测直线，平衡检测数量和质量
        """
        # 使用较低的初始阈值来检测更多线条
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=25)

        # 如果检测到过多线条，适度提高阈值
        if lines is not None and len(lines) > 200:
            print(f"检测到过多线条({len(lines)})，适度提高阈值...")
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=40)

        if lines is not None and len(lines) > 150:
            print(f"仍然较多线条({len(lines)})，进一步提高阈值...")
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=55)

        # 如果检测到的线条较少，降低阈值
        if lines is None or len(lines) < 15:
            print(f"检测到线条较少({len(lines) if lines is not None else 0})，降低阈值...")
            lines_low = cv2.HoughLines(edges, 1, np.pi/180, threshold=15)
            if lines_low is not None:
                if lines is None:
                    lines = lines_low
                else:
                    # 合并结果
                    combined = np.concatenate([lines, lines_low], axis=0)
                    if len(combined) <= 250:  # 进一步放宽总线条数限制
                        lines = combined
                print(f"降低阈值后检测到 {len(lines)} 条线")

        # 如果仍然检测到的线条很少，使用概率霍夫变换
        if lines is None or len(lines) < 8:
            print("线条较少，尝试概率霍夫变换...")
            lines_p = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=20,
                                     minLineLength=self.min_line_length,
                                     maxLineGap=self.max_line_gap)

            if lines_p is not None:
                # 将概率霍夫变换的结果转换为标准格式
                converted_lines = []
                for line in lines_p:
                    x1, y1, x2, y2 = line[0]

                    # 计算直线的rho和theta参数
                    if x2 == x1:  # 垂直线
                        rho = abs(x1)
                        theta = np.pi/2 if x1 >= 0 else -np.pi/2
                    else:
                        # 计算直线方程 y = mx + b，转换为 rho*cos(theta) + rho*sin(theta) = rho
                        dx = x2 - x1
                        dy = y2 - y1

                        # 计算角度
                        theta = np.arctan2(dy, dx)
                        if theta < 0:
                            theta += np.pi

                        # 计算rho（原点到直线的距离）
                        rho = abs(x1 * np.sin(theta) - y1 * np.cos(theta))

                        # 确保rho为正值
                        if x1 * np.sin(theta) - y1 * np.cos(theta) < 0:
                            theta += np.pi
                            if theta >= np.pi:
                                theta -= np.pi

                    converted_lines.append([[rho, theta]])

                lines = np.array(converted_lines) if converted_lines else None

        if lines is None:
            print("未检测到任何直线")
            return []

        print(f"霍夫变换检测到 {len(lines)} 条直线")

        # 轻度过滤重复和相近的线条
        if len(lines) > 100:  # 进一步提高过滤阈值
            print(f"检测到较多线条({len(lines)})，进行轻度预过滤...")
            # 按照rho值排序，去除过于相近的线条
            lines_sorted = sorted(lines, key=lambda x: x[0][0])  # 按rho排序
            filtered_lines = []
            for line in lines_sorted:
                rho, theta = line[0]
                # 使用宽松的重复判断标准
                is_duplicate = False
                for existing_line in filtered_lines:
                    existing_rho, existing_theta = existing_line[0]
                    if abs(rho - existing_rho) < 3 and abs(theta - existing_theta) < 0.03:  # 宽松的重复判断
                        is_duplicate = True
                        break
                if not is_duplicate:
                    filtered_lines.append(line)

            lines = np.array(filtered_lines)
            print(f"轻度预过滤后剩余 {len(lines)} 条直线")
        else:
            print(f"检测到 {len(lines)} 条线条，数量合理")



        detected_lines = []
        for line in lines:
            rho, theta = line[0]

            # 计算角度（度）
            angle_rad = theta
            angle_deg = np.degrees(angle_rad)

            # 标准化角度到 [0, 180) 度
            if angle_deg < 0:
                angle_deg += 180
            elif angle_deg >= 180:
                angle_deg -= 180

            # 将极坐标转换为直线上的两点
            a = np.cos(theta)
            b = np.sin(theta)
            x0 = a * rho
            y0 = b * rho

            # 计算直线上的两个点（延伸到图像边界）
            x1 = int(x0 + 1000 * (-b))
            y1 = int(y0 + 1000 * (a))
            x2 = int(x0 - 1000 * (-b))
            y2 = int(y0 - 1000 * (a))

            # 计算直线方程 ax + by + c = 0
            if abs(b) < 1e-6:  # 垂直线
                line_a, line_b, line_c = 1, 0, -rho
            else:
                line_a = a
                line_b = b
                line_c = -rho

            detected_lines.append({
                'rho': rho,
                'theta': theta,
                'angle': angle_deg,
                'line_eq': (line_a, line_b, line_c),
                'points': (x1, y1, x2, y2)
            })

        # 过滤质量较差的线条
        filtered_lines = self.filter_low_quality_lines(detected_lines)
        print(f"质量过滤后剩余 {len(filtered_lines)} 条直线")

        # 最终数量控制：如果仍然过多，选择质量最好的线条
        if len(filtered_lines) > 100:
            print(f"线条数量仍然过多({len(filtered_lines)})，选择质量最好的100条线...")
            filtered_lines = self.select_best_lines(filtered_lines, max_lines=100)
            print(f"最终选择 {len(filtered_lines)} 条最佳线条")

        return filtered_lines

    def select_best_lines(self, lines, max_lines=100):
        """
        从检测到的线条中选择质量最好的线条
        """
        if len(lines) <= max_lines:
            return lines

        # 计算每条线的质量分数
        scored_lines = []
        for line in lines:
            # 质量分数基于：
            # 1. rho值的稳定性（接近图像中心的线条通常更可靠）
            # 2. 角度的规律性（接近0°, 45°, 90°, 135°的线条更常见）
            rho = line['rho']
            angle = line['angle']

            # 角度分数：越接近主要方向（0°, 45°, 90°, 135°）分数越高
            angle_score = 0
            main_angles = [0, 45, 90, 135]
            for main_angle in main_angles:
                angle_diff = min(abs(angle - main_angle), abs(angle - main_angle + 180))
                if angle_diff <= 15:  # 15度容忍度
                    angle_score = max(angle_score, 1.0 - angle_diff / 15.0)

            # rho分数：适中的rho值通常更可靠
            rho_score = 1.0 / (1.0 + abs(rho) / 1000.0)  # rho越小分数越高

            total_score = angle_score * 0.7 + rho_score * 0.3
            scored_lines.append((total_score, line))

        # 按分数排序，选择最好的线条
        scored_lines.sort(key=lambda x: x[0], reverse=True)
        best_lines = [line for _, line in scored_lines[:max_lines]]

        return best_lines

    def filter_low_quality_lines(self, lines):
        """
        适度过滤质量较差的线条，保留更多有效线条
        """
        if not lines:
            return lines

        # 进行适度的质量检查
        filtered_lines = []

        # 计算所有线条的rho值分布，用于过滤极端异常值
        rho_values = [line['rho'] for line in lines]
        if rho_values:
            rho_mean = np.mean(rho_values)
            rho_std = np.std(rho_values)
            rho_threshold = rho_std * 3  # 3倍标准差阈值，更宽松

        for line in lines:
            # 过滤异常角度
            angle = line['angle']
            if not (0 <= angle <= 180):
                continue

            # 过滤异常的rho值
            rho = line['rho']
            if rho < 0 or rho > 15000:  # 放宽rho值范围
                continue

            # 只过滤极端偏离平均值的rho值
            if rho_values and len(rho_values) > 5 and abs(rho - rho_mean) > rho_threshold:
                continue

            filtered_lines.append(line)

        print(f"适度质量过滤：{len(lines)} -> {len(filtered_lines)} 条线")
        return filtered_lines
    
    def line_to_cartesian(self, rho, theta):
        """
        将极坐标形式的直线转换为笛卡尔坐标系中的两个点
        """
        a = np.cos(theta)
        b = np.sin(theta)
        x0 = a * rho
        y0 = b * rho
        
        # 计算直线上的两个点
        x1 = int(x0 + 1000 * (-b))
        y1 = int(y0 + 1000 * (a))
        x2 = int(x0 - 1000 * (-b))
        y2 = int(y0 - 1000 * (a))
        
        return (x1, y1), (x2, y2)
    
    def are_parallel(self, line1, line2):
        """
        判断两条直线是否平行，使用更宽松的条件
        """
        angle1 = line1['angle']
        angle2 = line2['angle']

        # 计算角度差
        angle_diff = abs(angle1 - angle2)

        # 考虑角度的周期性（0度和180度是同一方向）
        angle_diff = min(angle_diff, 180 - angle_diff)

        # 使用更宽松的角度阈值来检测更多平行线
        effective_threshold = self.angle_threshold * 1.5  # 增加50%的容忍度
        return angle_diff <= effective_threshold

    def calculate_distance_between_parallel_lines(self, line1, line2):
        """
        计算两条平行线之间的距离，使用改进的精确算法
        """
        # 方法1：使用rho值差异（对于平行线最直接）
        rho1, theta1 = line1['rho'], line1['theta']
        rho2, theta2 = line2['rho'], line2['theta']

        # 检查角度是否真的平行
        angle_diff = abs(theta1 - theta2)
        angle_diff = min(angle_diff, np.pi - angle_diff)

        if angle_diff < np.pi / 36:  # 5度以内认为是平行的
            # 对于真正平行的线，直接使用rho差值
            return abs(rho1 - rho2)

        # 方法2：使用点到直线距离公式（更通用）
        a1, b1, c1 = line1['line_eq']
        a2, b2, c2 = line2['line_eq']

        # 标准化直线方程系数
        norm1 = np.sqrt(a1*a1 + b1*b1)
        norm2 = np.sqrt(a2*a2 + b2*b2)

        if norm1 < 1e-6 or norm2 < 1e-6:
            return abs(rho1 - rho2)

        # 标准化后的系数
        a1_norm, b1_norm, c1_norm = a1/norm1, b1/norm1, c1/norm1
        a2_norm, b2_norm, c2_norm = a2/norm2, b2/norm2, c2/norm2

        # 确保法向量方向一致
        if a1_norm * a2_norm + b1_norm * b2_norm < 0:
            a2_norm, b2_norm, c2_norm = -a2_norm, -b2_norm, -c2_norm

        # 平行线间距离 = |c1 - c2|（当系数标准化后）
        distance = abs(c1_norm - c2_norm)

        return distance

    def merge_similar_lines(self, lines):
        """
        合并相似的线条以减少重复检测，特别处理粗线条产生的多条边缘
        """
        if not lines:
            return []

        print(f"开始合并相似线条，输入 {len(lines)} 条线")

        # 多轮合并，确保充分合并粗线条产生的多条边缘
        merged_lines = lines.copy()

        for round_num in range(3):  # 进行3轮合并
            print(f"第 {round_num + 1} 轮合并...")
            merged_lines = self.single_round_merge(merged_lines, round_num)

        print(f"合并完成，最终剩余 {len(merged_lines)} 条线")
        return merged_lines

    def single_round_merge(self, lines, round_num):
        """
        单轮线条合并
        """
        if not lines:
            return []

        merged_lines = []
        used = [False] * len(lines)

        # 根据轮次调整合并条件，更积极地合并
        if round_num == 0:
            # 第一轮：积极合并非常相近的线条（可能是同一条粗线的边缘）
            angle_threshold = 2.0
            distance_threshold = 15
        elif round_num == 1:
            # 第二轮：合并较相近的线条
            angle_threshold = 4.0
            distance_threshold = 30
        else:
            # 第三轮：合并相似的线条
            angle_threshold = 6.0
            distance_threshold = 50

        for i, line1 in enumerate(lines):
            if used[i]:
                continue

            similar_lines = [line1]
            used[i] = True

            for j, line2 in enumerate(lines):
                if used[j] or i == j:
                    continue

                # 检查是否为相似线条
                angle_diff = abs(line1['angle'] - line2['angle'])
                angle_diff = min(angle_diff, 180 - angle_diff)

                # 计算距离
                distance = self.calculate_distance_between_parallel_lines(line1, line2)

                # 使用当前轮次的合并条件
                if angle_diff <= angle_threshold and distance <= distance_threshold:
                    similar_lines.append(line2)
                    used[j] = True
                    print(f"    第{round_num+1}轮合并: 角度差={angle_diff:.1f}°, 距离={distance:.1f}")

            # 选择rho值的平均值作为代表
            if len(similar_lines) > 1:
                print(f"    将 {len(similar_lines)} 条相似线条合并为1条")
                avg_rho = np.mean([line['rho'] for line in similar_lines])
                avg_theta = np.mean([line['theta'] for line in similar_lines])
                avg_angle = np.mean([line['angle'] for line in similar_lines])

                # 重新计算直线方程
                a = np.cos(avg_theta)
                b = np.sin(avg_theta)
                c = -avg_rho

                x1 = int(a * avg_rho + 1000 * (-b))
                y1 = int(b * avg_rho + 1000 * (a))
                x2 = int(a * avg_rho - 1000 * (-b))
                y2 = int(b * avg_rho - 1000 * (a))

                merged_line = {
                    'rho': avg_rho,
                    'theta': avg_theta,
                    'angle': avg_angle,
                    'line_eq': (a, b, c),
                    'points': (x1, y1, x2, y2),
                    'merged_count': len(similar_lines)  # 记录合并的线条数量
                }
                merged_lines.append(merged_line)
            else:
                line1['merged_count'] = getattr(line1, 'merged_count', 1)
                merged_lines.append(line1)

        print(f"    第{round_num+1}轮合并结果: {len(lines)} -> {len(merged_lines)} 条线")
        return merged_lines

    def group_parallel_lines(self, lines):
        """
        将检测到的直线按平行关系分组
        """
        if not lines:
            return []

        # 首先合并相似的线条
        merged_lines = self.merge_similar_lines(lines)

        if len(merged_lines) < 2:
            print(f"合并后只剩 {len(merged_lines)} 条线，无法形成平行线组")
            return []

        # 如果线条数量仍然较少，放宽检测条件
        if len(merged_lines) < 10:
            print(f"线条数量较少({len(merged_lines)})，将使用更宽松的平行线检测条件")

        print(f"合并后剩余 {len(merged_lines)} 条线")

        # 打印所有线条的角度信息用于调试
        for i, line in enumerate(merged_lines):
            print(f"  线条 {i}: 角度={line['angle']:.1f}°, rho={line['rho']:.1f}")

        # 优先使用改进的精确检测算法
        parallel_groups = self.detect_strict_parallel_groups(merged_lines)

        # 如果精确检测结果较少，尝试多条平行线检测算法
        if len(parallel_groups) < 2:
            print("精确检测结果较少，尝试多条平行线检测算法...")
            parallel_groups_multi = self.detect_multiple_parallel_groups(merged_lines)
            if len(parallel_groups_multi) > len(parallel_groups):
                parallel_groups = parallel_groups_multi

        # 如果仍然较少，尝试改进的聚类方法
        if len(parallel_groups) < 2:
            print("检测到的平行线组仍然较少，尝试改进的聚类方法...")
            parallel_groups_improved = self.cluster_parallel_lines_improved(merged_lines)
            if len(parallel_groups_improved) > len(parallel_groups):
                parallel_groups = parallel_groups_improved

        return parallel_groups

    def detect_strict_parallel_groups(self, lines):
        """
        使用改进的精确条件检测平行线，提高准确性
        """
        if len(lines) < 2:
            return []

        print(f"精确平行线检测，输入 {len(lines)} 条线")

        # 第一步：按角度进行精确分组
        angle_groups = self.group_lines_by_precise_angle(lines)
        print(f"精确角度分组得到 {len(angle_groups)} 个角度组")

        # 第二步：对每个角度组进行精确的距离检查和分组
        final_groups = []
        for angle_key, angle_group in angle_groups.items():
            if len(angle_group) < 2:
                continue

            print(f"处理角度组 {angle_key:.1f}°，包含 {len(angle_group)} 条线")

            # 使用改进的距离分组算法
            distance_groups = self.group_lines_by_precise_distance(angle_group)
            final_groups.extend(distance_groups)

        print(f"精确检测最终得到 {len(final_groups)} 组平行线")
        return final_groups

    def group_lines_by_precise_angle(self, lines):
        """
        按角度进行精确分组，提高角度分组的准确性
        """
        angle_groups = {}

        # 使用更精确的角度容忍度
        angle_tolerance = min(self.angle_threshold * 0.8, 3.0)  # 最大不超过3度

        for i, line in enumerate(lines):
            angle = line['angle']

            # 将角度标准化到 [0, 180) 范围
            normalized_angle = angle % 180

            # 寻找最佳匹配的角度组
            best_match = None
            min_diff = float('inf')

            for group_angle in angle_groups.keys():
                # 计算角度差，考虑周期性
                diff1 = abs(normalized_angle - group_angle)
                diff2 = abs(normalized_angle - group_angle + 180)
                diff3 = abs(normalized_angle - group_angle - 180)
                angle_diff = min(diff1, diff2, diff3)

                if angle_diff <= angle_tolerance and angle_diff < min_diff:
                    min_diff = angle_diff
                    best_match = group_angle

            if best_match is not None:
                angle_groups[best_match].append((i, line))
            else:
                angle_groups[normalized_angle] = [(i, line)]

        # 过滤掉只有一条线的组
        filtered_groups = {k: v for k, v in angle_groups.items() if len(v) >= 2}

        return filtered_groups

    def group_lines_by_precise_distance(self, angle_group):
        """
        对同一角度组内的线条按距离进行精确分组
        """
        if len(angle_group) < 2:
            return []

        # 按rho值排序
        sorted_group = sorted(angle_group, key=lambda x: x[1]['rho'])

        # 计算合理的距离阈值
        distances = []
        for i in range(len(sorted_group) - 1):
            dist = self.calculate_distance_between_parallel_lines(
                sorted_group[i][1], sorted_group[i+1][1]
            )
            distances.append(dist)

        if distances:
            # 使用距离的统计信息来确定合理的分组阈值
            distances.sort()
            median_distance = distances[len(distances) // 2]

            # 动态调整距离阈值
            if median_distance < 20:
                distance_threshold = min(self.distance_threshold, median_distance * 3)
            else:
                distance_threshold = self.distance_threshold
        else:
            distance_threshold = self.distance_threshold

        print(f"    使用距离阈值: {distance_threshold:.1f}")

        # 使用连通性分析进行分组
        groups = []
        used = [False] * len(sorted_group)

        for i, (idx1, line1) in enumerate(sorted_group):
            if used[i]:
                continue

            # 开始新的组
            current_group = [(idx1, line1)]
            used[i] = True

            # 向前和向后寻找相邻的平行线
            self.expand_group_bidirectional(sorted_group, i, current_group, used, distance_threshold)

            # 验证组的质量
            if self.validate_parallel_group(current_group):
                groups.append(current_group)
                rho_values = [line[1]['rho'] for line in current_group]
                print(f"    形成精确平行线组: {len(current_group)} 条线, "
                      f"rho范围: {min(rho_values):.1f}-{max(rho_values):.1f}")

        return groups

    def expand_group_bidirectional(self, sorted_group, start_idx, current_group, used, distance_threshold):
        """
        双向扩展组，寻找相邻的平行线
        """
        # 向后扩展
        for j in range(start_idx + 1, len(sorted_group)):
            if used[j]:
                continue

            idx2, line2 = sorted_group[j]
            # 计算与组中最后一条线的距离
            last_line = current_group[-1][1]
            distance = self.calculate_distance_between_parallel_lines(last_line, line2)

            if distance <= distance_threshold:
                # 检查角度一致性
                angle_diff = abs(last_line['angle'] - line2['angle'])
                angle_diff = min(angle_diff, 180 - angle_diff)

                if angle_diff <= self.angle_threshold * 0.6:  # 更严格的角度要求
                    current_group.append((idx2, line2))
                    used[j] = True
                else:
                    break  # 角度不匹配，停止扩展
            else:
                break  # 距离太远，停止扩展

        # 向前扩展
        for j in range(start_idx - 1, -1, -1):
            if used[j]:
                continue

            idx2, line2 = sorted_group[j]
            # 计算与组中第一条线的距离
            first_line = current_group[0][1]
            distance = self.calculate_distance_between_parallel_lines(first_line, line2)

            if distance <= distance_threshold:
                # 检查角度一致性
                angle_diff = abs(first_line['angle'] - line2['angle'])
                angle_diff = min(angle_diff, 180 - angle_diff)

                if angle_diff <= self.angle_threshold * 0.6:  # 更严格的角度要求
                    current_group.insert(0, (idx2, line2))
                    used[j] = True
                else:
                    break  # 角度不匹配，停止扩展
            else:
                break  # 距离太远，停止扩展

    def validate_parallel_group(self, group):
        """
        验证平行线组的质量
        """
        if len(group) < 2:
            return False

        # 检查组内线条的角度一致性
        angles = [line[1]['angle'] for _, line in group]
        angle_std = np.std(angles)

        # 角度标准差应该很小
        if angle_std > self.angle_threshold * 0.5:
            return False

        # 检查距离的规律性（可选）
        if len(group) >= 3:
            rho_values = [line[1]['rho'] for _, line in group]
            rho_values.sort()

            # 计算相邻线条间距的变异系数
            distances = [rho_values[i+1] - rho_values[i] for i in range(len(rho_values)-1)]
            if distances:
                distance_mean = np.mean(distances)
                distance_std = np.std(distances)

                # 如果距离变异过大，可能不是真正的平行线组
                if distance_mean > 0 and distance_std / distance_mean > 1.0:
                    return False

        return True

    def detect_multiple_parallel_groups(self, lines):
        """
        专门用于检测多条平行线的新算法
        """
        if len(lines) < 2:
            return []

        print(f"使用多条平行线检测算法，输入 {len(lines)} 条线")

        # 第一步：按角度分组
        angle_groups = self.group_lines_by_angle(lines)

        # 第二步：对每个角度组进行距离聚类
        all_parallel_groups = []
        for angle_key, angle_group in angle_groups.items():
            if len(angle_group) >= 2:
                distance_groups = self.cluster_by_distance(angle_group, angle_key)
                all_parallel_groups.extend(distance_groups)

        print(f"多条平行线检测完成，共检测到 {len(all_parallel_groups)} 组")
        return all_parallel_groups

    def group_lines_by_angle(self, lines):
        """
        按角度对线条进行分组
        """
        angle_groups = {}
        angle_tolerance = self.angle_threshold * 2.5  # 进一步放宽角度容忍度

        for i, line in enumerate(lines):
            angle = line['angle']

            # 标准化角度到 [0, 90) 范围
            normalized_angle = angle % 90

            # 寻找最匹配的角度组
            best_match = None
            min_diff = float('inf')

            for existing_angle in angle_groups.keys():
                diff = abs(normalized_angle - existing_angle)
                # 考虑角度的周期性
                diff = min(diff, 90 - diff)

                if diff <= angle_tolerance and diff < min_diff:
                    min_diff = diff
                    best_match = existing_angle

            if best_match is not None:
                angle_groups[best_match].append((i, line))
            else:
                angle_groups[normalized_angle] = [(i, line)]

        print(f"按角度分组：{len(angle_groups)} 个角度组")
        for angle, group in angle_groups.items():
            print(f"  角度 {angle:.1f}°: {len(group)} 条线")

        return angle_groups

    def cluster_by_distance(self, angle_group, angle_key):
        """
        对同一角度的线条按距离进行聚类
        """
        if len(angle_group) < 2:
            return []

        print(f"对角度 {angle_key:.1f}° 的 {len(angle_group)} 条线进行距离聚类")

        # 按rho值排序
        sorted_group = sorted(angle_group, key=lambda x: x[1]['rho'])

        # 计算所有线条之间的距离矩阵
        n = len(sorted_group)
        distance_matrix = [[0] * n for _ in range(n)]

        for i in range(n):
            for j in range(i + 1, n):
                dist = self.calculate_distance_between_parallel_lines(
                    sorted_group[i][1], sorted_group[j][1]
                )
                distance_matrix[i][j] = dist
                distance_matrix[j][i] = dist

        # 使用连通性分析进行聚类
        visited = [False] * n
        clusters = []

        # 动态确定距离阈值
        all_distances = []
        for i in range(n):
            for j in range(i + 1, n):
                all_distances.append(distance_matrix[i][j])

        if all_distances:
            all_distances.sort()
            # 使用更大的距离阈值以确保能检测到更多平行线
            distance_threshold = min(self.distance_threshold * 6,
                                   all_distances[len(all_distances) // 2] * 4)  # 使用中位数的4倍
            print(f"  使用距离阈值: {distance_threshold:.1f}")
        else:
            distance_threshold = self.distance_threshold * 3

        for i in range(n):
            if visited[i]:
                continue

            # 开始新的聚类
            cluster = []
            stack = [i]

            while stack:
                current = stack.pop()
                if visited[current]:
                    continue

                visited[current] = True
                cluster.append(sorted_group[current])

                # 寻找距离在阈值内的邻居
                for j in range(n):
                    if not visited[j] and distance_matrix[current][j] <= distance_threshold:
                        stack.append(j)

            # 只保留包含至少2条线的聚类
            if len(cluster) >= 2:
                clusters.append(cluster)
                rho_values = [line[1]['rho'] for line in cluster]
                print(f"    聚类: {len(cluster)} 条线, rho范围: {min(rho_values):.1f}-{max(rho_values):.1f}")

        return clusters

    def cluster_parallel_lines_improved(self, lines):
        """
        完全重写的平行线聚类方法，专门处理多条平行线的情况
        """
        if len(lines) < 2:
            return []

        print(f"开始改进聚类，输入 {len(lines)} 条线")

        # 第一步：按角度进行粗分组，使用更精确的角度处理
        angle_groups = self.group_by_angle(lines)
        print(f"按角度分组得到 {len(angle_groups)} 个角度组")

        # 第二步：对每个角度组内的线条按距离进行精细分组
        final_groups = []
        for angle_key, angle_group in angle_groups.items():
            if len(angle_group) < 2:
                continue

            print(f"处理角度组 {angle_key:.1f}°，包含 {len(angle_group)} 条线")
            distance_groups = self.group_by_distance(angle_group)
            final_groups.extend(distance_groups)

        print(f"改进聚类最终检测到 {len(final_groups)} 组平行线")
        return final_groups

    def group_by_angle(self, lines):
        """
        按角度对线条进行分组
        """
        angle_groups = {}
        angle_tolerance = self.angle_threshold * 2.2  # 更宽松的角度容忍度

        for i, line in enumerate(lines):
            angle = line['angle']

            # 标准化角度：将所有角度映射到 [0, 90) 范围
            # 因为平行线的角度可能相差180度
            if angle >= 90:
                normalized_angle = angle - 90
            else:
                normalized_angle = angle

            # 寻找相近角度的组
            found_group = False
            best_match_angle = None
            min_angle_diff = float('inf')

            for group_angle in angle_groups.keys():
                angle_diff = abs(normalized_angle - group_angle)
                # 处理角度的周期性（0度和90度相近）
                angle_diff = min(angle_diff, 90 - angle_diff)

                if angle_diff <= angle_tolerance and angle_diff < min_angle_diff:
                    min_angle_diff = angle_diff
                    best_match_angle = group_angle
                    found_group = True

            if found_group:
                angle_groups[best_match_angle].append((i, line))
            else:
                angle_groups[normalized_angle] = [(i, line)]

        return angle_groups

    def group_by_distance(self, angle_group):
        """
        对同一角度组内的线条按距离进行分组
        """
        if len(angle_group) < 2:
            return []

        # 按rho值排序，便于距离分组
        sorted_lines = sorted(angle_group, key=lambda x: x[1]['rho'])

        distance_groups = []
        used = [False] * len(sorted_lines)

        for i, (idx1, line1) in enumerate(sorted_lines):
            if used[i]:
                continue

            # 开始一个新的距离组
            current_group = [(idx1, line1)]
            used[i] = True

            # 寻找距离合适的其他线条
            for j, (idx2, line2) in enumerate(sorted_lines):
                if used[j] or i == j:
                    continue

                # 计算与当前组中所有线条的距离
                distances_to_group = []
                for _, group_line in current_group:
                    dist = self.calculate_distance_between_parallel_lines(group_line, line2)
                    distances_to_group.append(dist)

                min_dist = min(distances_to_group)
                max_dist = max(distances_to_group)

                # 动态调整距离阈值
                # 如果是密集的平行线，使用较小的阈值
                # 如果是稀疏的平行线，使用较大的阈值
                if len(sorted_lines) > 5:  # 密集情况
                    max_allowed_distance = self.distance_threshold * 2.0
                    min_allowed_distance = 1
                else:  # 稀疏情况
                    max_allowed_distance = self.distance_threshold * 4.0
                    min_allowed_distance = 1

                # 检查是否可以加入当前组
                if (min_allowed_distance <= min_dist <= max_allowed_distance and
                    max_dist <= max_allowed_distance * 1.5):  # 放宽组内距离限制

                    current_group.append((idx2, line2))
                    used[j] = True
                    print(f"      线条 {idx2} 加入组 (距离范围: {min_dist:.1f}-{max_dist:.1f})")

            # 只保留包含至少2条线的组
            if len(current_group) >= 2:
                distance_groups.append(current_group)
                rho_values = [line[1]['rho'] for line in current_group]
                print(f"    形成距离组: {len(current_group)} 条线, rho范围: {min(rho_values):.1f}-{max(rho_values):.1f}")

        return distance_groups

    def detect_parallel_lines(self, image_path, debug=True):
        """
        主函数：检测图片中的平行线组
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")

        print(f"图像尺寸: {image.shape}")

        # 预处理
        edges, gray = self.preprocess_image(image)

        if debug:
            # 保存边缘检测结果用于调试
            cv2.imwrite("debug_edges.jpg", edges)
            print("边缘检测结果已保存到 debug_edges.jpg")

        # 检测直线
        lines = self.detect_lines(edges)

        if not lines:
            print("未检测到任何直线")
            if debug:
                print("调试建议:")
                print("1. 检查图像对比度是否足够")
                print("2. 尝试调整Canny边缘检测参数")
                print("3. 降低霍夫变换阈值")
                print("4. 检查图像中是否确实存在直线")
            return [], image, edges, gray, []

        print(f"检测到 {len(lines)} 条线段")

        # 分组平行线
        parallel_groups = self.group_parallel_lines(lines)

        print(f"检测到 {len(parallel_groups)} 组平行线")

        # 输出每组的详细信息
        for i, group in enumerate(parallel_groups):
            angle = group[0][1]['angle']  # 获取第一条线的角度
            print(f"  第 {i+1} 组: {len(group)} 条平行线, 角度: {angle:.1f}°")

        return parallel_groups, image, edges, gray, lines
    
    def clip_line_to_image(self, x1, y1, x2, y2, width, height):
        """
        将直线裁剪到图像边界内
        """
        # 使用Liang-Barsky算法裁剪直线
        def clip_test(p, q, u1, u2):
            if p < 0:
                r = q / p
                if r > u2:
                    return False, u1, u2
                elif r > u1:
                    u1 = r
            elif p > 0:
                r = q / p
                if r < u1:
                    return False, u1, u2
                elif r < u2:
                    u2 = r
            elif q < 0:
                return False, u1, u2
            return True, u1, u2

        dx = x2 - x1
        dy = y2 - y1
        u1, u2 = 0.0, 1.0

        # 左边界
        accept, u1, u2 = clip_test(-dx, x1, u1, u2)
        if not accept:
            return None

        # 右边界
        accept, u1, u2 = clip_test(dx, width - 1 - x1, u1, u2)
        if not accept:
            return None

        # 下边界
        accept, u1, u2 = clip_test(-dy, y1, u1, u2)
        if not accept:
            return None

        # 上边界
        accept, u1, u2 = clip_test(dy, height - 1 - y1, u1, u2)
        if not accept:
            return None

        if u2 < 1:
            x2 = x1 + u2 * dx
            y2 = y1 + u2 * dy

        if u1 > 0:
            x1 = x1 + u1 * dx
            y1 = y1 + u1 * dy

        return int(x1), int(y1), int(x2), int(y2)

    def visualize_results(self, image, parallel_groups, all_lines=None, save_path=None, show_all_lines=False):
        """
        可视化检测结果
        """
        result_image = image.copy()
        height, width = image.shape[:2]

        # 为每组平行线分配不同的颜色
        colors = [
            (0, 0, 255),    # 红色 (BGR格式)
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 255, 255),  # 黄色
            (255, 0, 255),  # 品红色
            (255, 255, 0),  # 青色
            (128, 0, 128),  # 紫色
            (0, 165, 255),  # 橙色
            (128, 128, 0),  # 橄榄色
            (128, 0, 0),    # 栗色
        ]

        # 如果需要显示所有检测到的线条（灰色）
        if show_all_lines and all_lines:
            for line in all_lines:
                x1, y1, x2, y2 = line['points']
                clipped = self.clip_line_to_image(x1, y1, x2, y2, width, height)
                if clipped:
                    cv2.line(result_image, (clipped[0], clipped[1]),
                            (clipped[2], clipped[3]), (128, 128, 128), 5)  # 进一步增加背景线条粗细到5像素

        # 绘制平行线组
        for group_idx, group in enumerate(parallel_groups):
            color = colors[group_idx % len(colors)]

            for _, line in group:
                x1, y1, x2, y2 = line['points']
                clipped = self.clip_line_to_image(x1, y1, x2, y2, width, height)
                if clipped:
                    cv2.line(result_image, (clipped[0], clipped[1]),
                            (clipped[2], clipped[3]), color, 12)  # 进一步增加线条粗细到12像素

        # 添加文本标注
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(result_image, f'Parallel Groups: {len(parallel_groups)}',
                   (10, 35), font, 1.2, (255, 255, 255), 3)  # 增加字体大小和粗细

        for i, group in enumerate(parallel_groups):
            angle = group[0][1]['angle']  # 获取第一条线的角度
            text = f'Group {i+1}: {len(group)} lines, {angle:.1f}°'
            cv2.putText(result_image, text, (10, 70 + i * 30),  # 调整间距
                       font, 0.8, colors[i % len(colors)], 3)  # 增加字体大小和粗细

        if save_path:
            cv2.imwrite(save_path, result_image)
            print(f"结果图像已保存到: {save_path}")

        return result_image

    def count_parallel_pairs(self, parallel_groups: List[List[Tuple[int, dict]]]) -> Tuple[int, List[int]]:
        """
        计算平行线对数：每组含 n 条线，其平行线对数为 C(n, 2) = n*(n-1)/2。
        返回总对数与各组对数列表。
        """
        group_pairs = []
        for group in parallel_groups:
            n = len(group)
            group_pairs.append(n * (n - 1) // 2)
        total_pairs = sum(group_pairs)
        return total_pairs, group_pairs

def create_test_image():
    """
    创建一个包含平行线的测试图像
    """
    # 创建一个白色背景的图像
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255

    # 绘制第一组平行线（水平方向）
    y_positions = [100, 150, 200]
    for y in y_positions:
        cv2.line(image, (50, y), (750, y), (0, 0, 0), 3)

    # 绘制第二组平行线（倾斜方向）
    start_points = [(100, 300), (150, 350), (200, 400)]
    end_points = [(600, 400), (650, 450), (700, 500)]
    for start, end in zip(start_points, end_points):
        cv2.line(image, start, end, (0, 0, 0), 3)

    # 绘制第三组平行线（垂直方向）
    x_positions = [300, 350, 400]
    for x in x_positions:
        cv2.line(image, (x, 50), (x, 250), (0, 0, 0), 3)

    # 添加一些噪声线（非平行）
    cv2.line(image, (500, 100), (600, 300), (128, 128, 128), 2)
    cv2.line(image, (50, 500), (200, 550), (128, 128, 128), 2)

    return image

def main():
    """
    主函数示例
    """
    print("=" * 60)
    print("🔍 平行线检测算法演示")
    print("=" * 60)

    # 创建测试图像
    print("创建测试图像...")
    test_image = create_test_image()
    test_image_path = "D:/1/4.jpg"
    cv2.imwrite(test_image_path, test_image)
    print(f"✅ 测试图像已保存: {test_image_path}")

    # 创建检测器实例，使用更宽松的参数以检测更多平行线
    detector = ParallelLineDetector(
        angle_threshold=8.0,    # 进一步增加角度容忍度
        distance_threshold=80,  # 进一步增加距离阈值
        min_line_length=20,     # 进一步降低最小线段长度
        max_line_gap=30         # 进一步增加线段间隙容忍度
    )

    try:
        print(f"\n开始检测平行线...")
        parallel_groups, original_image, edges, gray, all_lines = detector.detect_parallel_lines(test_image_path)

        # 输出结果
        print(f"\n=== 平行线检测结果 ===")
        print(f"总共检测到 {len(parallel_groups)} 组平行线")

        total_parallel_lines = sum(len(group) for group in parallel_groups)
        total_pairs, group_pairs = detector.count_parallel_pairs(parallel_groups)
        print(f"平行线条数: {total_parallel_lines}")
        print(f"平行线对数: {total_pairs}")
        for idx, (group, pair_cnt) in enumerate(zip(parallel_groups, group_pairs)):
            print(f"  第{idx+1}组: {len(group)} 条线, {pair_cnt} 对")

        # 可视化结果
        result_image = detector.visualize_results(
            original_image,
            parallel_groups,
            all_lines,
            save_path="parallel_lines_result.jpg",
            show_all_lines=True
        )

        # 使用matplotlib显示结果
        plt.figure(figsize=(20, 5))

        plt.subplot(1, 4, 1)
        plt.imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        plt.title("原始图像")
        plt.axis('off')

        plt.subplot(1, 4, 2)
        plt.imshow(gray, cmap='gray')
        plt.title("灰度图像")
        plt.axis('off')

        plt.subplot(1, 4, 3)
        plt.imshow(edges, cmap='gray')
        plt.title("边缘检测")
        plt.axis('off')

        plt.subplot(1, 4, 4)
        plt.imshow(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        plt.title(f"平行线检测结果\n({len(parallel_groups)} 组平行线)")
        plt.axis('off')

        plt.tight_layout()
        plt.savefig("detection_process.png", dpi=150, bbox_inches='tight')
        plt.show()

        print(f"\n✅ 检测完成!")
        print(f"📊 结果图像: parallel_lines_result.jpg")
        print(f"📊 处理过程: detection_process.png")

    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
