#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的粗线条检测算法
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from parallel_line_detector import ParallelLineDetector

def create_thick_line_test_image():
    """
    创建一个包含粗线条的测试图像
    """
    # 创建一个白色背景的图像
    height, width = 600, 800
    image = np.ones((height, width, 3), dtype=np.uint8) * 255

    # 第一组：粗水平平行线
    y_positions = [100, 180, 260]
    for y in y_positions:
        cv2.line(image, (50, y), (750, y), (0, 0, 0), 15)  # 15像素粗的线

    # 第二组：粗倾斜平行线
    start_points = [(100, 350), (180, 430)]
    for start in start_points:
        end_x = start[0] + 500
        end_y = start[1] + 100
        cv2.line(image, start, (end_x, end_y), (0, 0, 0), 12)  # 12像素粗的线

    # 第三组：粗垂直平行线
    x_positions = [300, 400, 500]
    for x in x_positions:
        cv2.line(image, (x, 50), (x, 300), (0, 0, 0), 10)  # 10像素粗的线

    # 添加一些细线条作为对比
    cv2.line(image, (600, 100), (700, 200), (128, 128, 128), 2)
    cv2.line(image, (650, 150), (750, 250), (128, 128, 128), 2)

    return image

def test_thick_line_detection():
    """
    测试粗线条检测效果
    """
    print("=" * 80)
    print("🔍 测试粗线条检测算法")
    print("=" * 80)

    # 创建粗线条测试图像
    print("创建粗线条测试图像...")
    test_image = create_thick_line_test_image()
    test_image_path = "thick_line_test.jpg"
    cv2.imwrite(test_image_path, test_image)
    print(f"✅ 粗线条测试图像已保存: {test_image_path}")

    # 测试不同的检测策略
    test_configs = [
        {
            "name": "传统边缘检测",
            "use_skeletonization": False,
            "params": {
                "angle_threshold": 5.0,
                "distance_threshold": 50,
                "min_line_length": 30,
                "max_line_gap": 15
            }
        },
        {
            "name": "骨架化检测",
            "use_skeletonization": True,
            "params": {
                "angle_threshold": 6.0,
                "distance_threshold": 60,
                "min_line_length": 25,
                "max_line_gap": 20
            }
        }
    ]

    results = []
    
    for i, config in enumerate(test_configs):
        print(f"\n{'='*60}")
        print(f"测试配置 {i+1}: {config['name']}")
        print(f"{'='*60}")
        
        # 创建检测器
        detector = ParallelLineDetector(**config['params'])
        
        # 如果是传统检测，临时修改预处理方法
        if not config['use_skeletonization']:
            # 保存原始方法
            original_preprocess = detector.preprocess_image
            # 使用传统预处理
            detector.preprocess_image = lambda img: traditional_preprocess(img)
        
        try:
            # 执行检测
            parallel_groups, original_image, edges, gray, all_lines = detector.detect_parallel_lines(
                test_image_path, debug=True
            )
            
            # 统计结果
            total_groups = len(parallel_groups)
            total_lines = sum(len(group) for group in parallel_groups)
            total_pairs, group_pairs = detector.count_parallel_pairs(parallel_groups)
            
            result = {
                "config": config["name"],
                "groups": total_groups,
                "lines": total_lines,
                "pairs": total_pairs,
                "all_detected_lines": len(all_lines) if all_lines else 0
            }
            results.append(result)
            
            print(f"\n📊 检测结果:")
            print(f"  - 检测到的所有线条: {result['all_detected_lines']}")
            print(f"  - 平行线组数: {result['groups']}")
            print(f"  - 平行线条数: {result['lines']}")
            print(f"  - 平行线对数: {result['pairs']}")
            
            # 输出每组的详细信息
            for j, group in enumerate(parallel_groups):
                angle = group[0][1]['angle']
                print(f"    第 {j+1} 组: {len(group)} 条线, 角度: {angle:.1f}°")
            
            # 可视化结果
            result_image = detector.visualize_results(
                original_image,
                parallel_groups,
                all_lines,
                save_path=f"thick_line_result_{i+1}_{config['name'].replace(' ', '_')}.jpg",
                show_all_lines=True
            )
            
            # 保存边缘检测结果用于对比
            cv2.imwrite(f"edges_{i+1}_{config['name'].replace(' ', '_')}.jpg", edges)
            
        except Exception as e:
            print(f"❌ 检测失败: {e}")
            import traceback
            traceback.print_exc()
            
            result = {
                "config": config["name"],
                "groups": 0,
                "lines": 0,
                "pairs": 0,
                "all_detected_lines": 0,
                "error": str(e)
            }
            results.append(result)

    # 输出对比结果
    print(f"\n{'='*80}")
    print("📈 粗线条检测结果对比")
    print(f"{'='*80}")
    print(f"{'检测方法':<20} {'总线条':<10} {'平行组':<10} {'平行线':<10} {'平行对':<10}")
    print("-" * 80)
    
    for result in results:
        if "error" not in result:
            print(f"{result['config']:<20} {result['all_detected_lines']:<10} "
                  f"{result['groups']:<10} {result['lines']:<10} {result['pairs']:<10}")
        else:
            print(f"{result['config']:<20} {'ERROR':<10} {'ERROR':<10} {'ERROR':<10} {'ERROR':<10}")

    print(f"\n✅ 粗线条检测测试完成!")
    print(f"📁 结果图像已保存到当前目录")
    
    return results

def traditional_preprocess(image):
    """
    传统的预处理方法，用于对比
    """
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # 高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 传统Canny边缘检测
    median_val = np.median(blurred)
    lower_threshold = int(max(30, 0.6 * median_val))
    upper_threshold = int(min(200, 1.2 * median_val))
    edges = cv2.Canny(blurred, lower_threshold, upper_threshold, apertureSize=3)
    
    return edges, gray

if __name__ == "__main__":
    test_thick_line_detection()
