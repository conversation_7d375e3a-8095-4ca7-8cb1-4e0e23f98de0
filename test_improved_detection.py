#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的平行线检测算法
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from parallel_line_detector import ParallelLineDetector

def create_complex_test_image():
    """
    创建一个包含更多平行线的复杂测试图像
    """
    # 创建一个白色背景的图像
    height, width = 800, 1000
    image = np.ones((height, width, 3), dtype=np.uint8) * 255

    # 第一组：水平平行线（5条）
    y_positions = [80, 120, 160, 200, 240]
    for y in y_positions:
        cv2.line(image, (50, y), (950, y), (0, 0, 0), 4)

    # 第二组：倾斜平行线（4条）
    start_points = [(100, 300), (150, 350), (200, 400), (250, 450)]
    for i, start in enumerate(start_points):
        end_x = start[0] + 600
        end_y = start[1] + 150
        cv2.line(image, start, (end_x, end_y), (0, 0, 0), 4)

    # 第三组：垂直平行线（6条）
    x_positions = [300, 340, 380, 420, 460, 500]
    for x in x_positions:
        cv2.line(image, (x, 50), (x, 280), (0, 0, 0), 4)

    # 第四组：另一个角度的平行线（3条）
    start_points_2 = [(600, 100), (650, 150), (700, 200)]
    for start in start_points_2:
        end_x = start[0] + 200
        end_y = start[1] + 300
        cv2.line(image, start, (end_x, end_y), (0, 0, 0), 4)

    # 第五组：接近水平但稍有倾斜的平行线（4条）
    for i, y_base in enumerate([500, 530, 560, 590]):
        start_y = y_base
        end_y = y_base + 20  # 轻微倾斜
        cv2.line(image, (100, start_y), (800, end_y), (0, 0, 0), 4)

    # 添加一些噪声线（非平行）
    cv2.line(image, (50, 700), (200, 750), (128, 128, 128), 3)
    cv2.line(image, (800, 600), (900, 700), (128, 128, 128), 3)
    cv2.line(image, (600, 650), (750, 680), (128, 128, 128), 3)

    return image

def test_detection_with_different_parameters():
    """
    使用不同参数测试平行线检测
    """
    print("=" * 80)
    print("🔍 测试改进后的平行线检测算法")
    print("=" * 80)

    # 创建复杂测试图像
    print("创建复杂测试图像...")
    test_image = create_complex_test_image()
    test_image_path = "complex_test_image.jpg"
    cv2.imwrite(test_image_path, test_image)
    print(f"✅ 复杂测试图像已保存: {test_image_path}")

    # 测试不同的参数配置
    test_configs = [
        {
            "name": "默认参数（改进前）",
            "params": {
                "angle_threshold": 3.0,
                "distance_threshold": 30,
                "min_line_length": 50,
                "max_line_gap": 10
            }
        },
        {
            "name": "宽松参数（改进后）",
            "params": {
                "angle_threshold": 6.0,
                "distance_threshold": 60,
                "min_line_length": 25,
                "max_line_gap": 20
            }
        },
        {
            "name": "超宽松参数",
            "params": {
                "angle_threshold": 8.0,
                "distance_threshold": 80,
                "min_line_length": 20,
                "max_line_gap": 25
            }
        }
    ]

    results = []
    
    for i, config in enumerate(test_configs):
        print(f"\n{'='*60}")
        print(f"测试配置 {i+1}: {config['name']}")
        print(f"{'='*60}")
        
        # 创建检测器
        detector = ParallelLineDetector(**config['params'])
        
        try:
            # 执行检测
            parallel_groups, original_image, edges, gray, all_lines = detector.detect_parallel_lines(
                test_image_path, debug=True
            )
            
            # 统计结果
            total_groups = len(parallel_groups)
            total_lines = sum(len(group) for group in parallel_groups)
            total_pairs, group_pairs = detector.count_parallel_pairs(parallel_groups)
            
            result = {
                "config": config["name"],
                "groups": total_groups,
                "lines": total_lines,
                "pairs": total_pairs,
                "all_detected_lines": len(all_lines) if all_lines else 0
            }
            results.append(result)
            
            print(f"\n📊 检测结果:")
            print(f"  - 检测到的所有线条: {result['all_detected_lines']}")
            print(f"  - 平行线组数: {result['groups']}")
            print(f"  - 平行线条数: {result['lines']}")
            print(f"  - 平行线对数: {result['pairs']}")
            
            # 输出每组的详细信息
            for j, group in enumerate(parallel_groups):
                angle = group[0][1]['angle']
                print(f"    第 {j+1} 组: {len(group)} 条线, 角度: {angle:.1f}°")
            
            # 可视化结果
            result_image = detector.visualize_results(
                original_image,
                parallel_groups,
                all_lines,
                save_path=f"result_{i+1}_{config['name'].replace(' ', '_').replace('（', '').replace('）', '')}.jpg",
                show_all_lines=True
            )
            
        except Exception as e:
            print(f"❌ 检测失败: {e}")
            import traceback
            traceback.print_exc()
            
            result = {
                "config": config["name"],
                "groups": 0,
                "lines": 0,
                "pairs": 0,
                "all_detected_lines": 0,
                "error": str(e)
            }
            results.append(result)

    # 输出对比结果
    print(f"\n{'='*80}")
    print("📈 检测结果对比")
    print(f"{'='*80}")
    print(f"{'配置':<20} {'总线条':<10} {'平行组':<10} {'平行线':<10} {'平行对':<10}")
    print("-" * 80)
    
    for result in results:
        if "error" not in result:
            print(f"{result['config']:<20} {result['all_detected_lines']:<10} "
                  f"{result['groups']:<10} {result['lines']:<10} {result['pairs']:<10}")
        else:
            print(f"{result['config']:<20} {'ERROR':<10} {'ERROR':<10} {'ERROR':<10} {'ERROR':<10}")

    print(f"\n✅ 测试完成!")
    print(f"📁 结果图像已保存到当前目录")
    
    return results

if __name__ == "__main__":
    test_detection_with_different_parameters()
